# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Rûpela berê
previous_label=Paşve
next.title=Rûpela pêş
next_label=Pêş

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=Dûr bike
zoom_out_label=Dûr bike
zoom_in.title=Nêzîk bike
zoom_in_label=Nêzîk bike
zoom.title=Nêzîk Bike
presentation_mode.title=Derbasî mûda pêşkêşkariyê bibe
presentation_mode_label=Moda Pêşkêşkariyê
open_file.title=Pelî veke
open_file_label=Veke
print.title=Çap bike
print_label=Çap bike
download.title=Jêbar bike
download_label=Jêbar bike
bookmark.title=Xuyakirina niha (kopî yan jî di pencereyeke nû de veke)
bookmark_label=Xuyakirina niha

# Secondary toolbar and context menu
tools.title=Amûr
tools_label=Amûr
first_page.title=Here rûpela yekemîn
first_page.label=Here rûpela yekemîn
first_page_label=Here rûpela yekemîn
last_page.title=Here rûpela dawîn
last_page.label=Here rûpela dawîn
last_page_label=Here rûpela dawîn
page_rotate_cw.title=Bi aliyê saetê ve bizivirîne
page_rotate_cw.label=Bi aliyê saetê ve bizivirîne
page_rotate_cw_label=Bi aliyê saetê ve bizivirîne
page_rotate_ccw.title=Berevajî aliyê saetê ve bizivirîne
page_rotate_ccw.label=Berevajî aliyê saetê ve bizivirîne
page_rotate_ccw_label=Berevajî aliyê saetê ve bizivirîne


# Document properties dialog box
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_title=Sernav:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Darikê kêlekê veke/bigire
toggle_sidebar_label=Darikê kêlekê veke/bigire
document_outline_label=Şemaya belgeyê
thumbs.title=Wênekokan nîşan bide
thumbs_label=Wênekok
findbar.title=Di belgeyê de bibîne

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Rûpel {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Wênekoka rûpelê {{page}}

# Find panel button title and messages
find_previous.title=Peyva berê bibîne
find_previous_label=Paşve
find_next.title=Peyya pêş bibîne
find_next_label=Pêşve
find_highlight=Tevî beloq bike
find_match_case_label=Ji bo tîpên hûrdek-girdek bihîstyar
find_reached_top=Gihîşt serê rûpelê, ji dawiya rûpelê bidomîne
find_reached_bottom=Gihîşt dawiya rûpelê, ji serê rûpelê bidomîne
find_not_found=Peyv nehat dîtin

# Error panel labels
error_more_info=Zêdetir agahî
error_less_info=Zêdetir agahî
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js versiyon {{version}} (avanî: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Peyam: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Komik: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Pel: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Rêzik: {{line}}
rendering_error=Di vehûrandina rûpelê de çewtî çêbû.

# Predefined zoom values
page_scale_width=Firehiya rûpelê
page_scale_fit=Di rûpelê de bicî bike
page_scale_auto=Xweber nêzîk bike
page_scale_actual=Mezinahiya rastîn
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=Xeletî
loading_error=Dema ku PDF dihat barkirin çewtiyek çêbû.
invalid_file_error=Pelê PDFê nederbasdar yan jî xirabe ye.
missing_file_error=Pelê PDFê kêm e.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Nîşaneya {{type}}ê]
password_label=Ji bo PDFê vekî şîfreyê binivîse.
password_invalid=Şîfre çewt e. Tika ye dîsa biceribîne.
password_ok=Temam

printing_not_supported=Hişyarî: Çapkirin ji hêla vê gerokê ve bi temamî nayê destekirin.
printing_not_ready=Hişyarî: PDF bi temamî nehat barkirin û ji bo çapê ne amade ye.
web_fonts_disabled=Fontên Webê neçalak in: Fontên PDFê yên veşartî nayên bikaranîn.
document_colors_not_allowed=Destûr tune ye ku belgeyên PDFê rengên xwe bi kar bînin: Di gerokê de 'destûrê bide rûpelan ku rengên xwe bi kar bînin' nehatiye çalakirin.
