<p align="center">
    <img src="./logo/logo-animate.svg" width="100%" height="300" alt="clippy the assistant">
</p>

<p align="center">
    Easily generate and modify .docx files with JS/TS. Works for Node and on the Browser.
</p>

---

[![NPM version][npm-image]][npm-url]
[![Downloads per month][downloads-image]][downloads-url]
[![GitHub Action Workflow Status][github-actions-workflow-image]][github-actions-workflow-url]
[![Known Vulnerabilities][snky-image]][snky-url]
[![PRs Welcome][pr-image]][pr-url]
[![codecov][codecov-image]][codecov-url]

<p align="center">
    <img src="https://i.imgur.com/QeL1HuU.png" alt="drawing"/>
</p>

# Demo

## Browser

Here are examples of `docx` being used with basic `HTML/JS` in a browser environment:

-   https://codepen.io/dolanmiu/pen/RwNeObg
-   https://jsfiddle.net/dolanmiu/onadx1gu/

Here are examples of `docx` working in `Angular`:

-   https://stackblitz.com/edit/angular-docx
-   https://stackblitz.com/edit/angular-wmd6k3

Here are examples of `docx` working in `React`:

-   https://stackblitz.com/edit/react-docx
-   https://stackblitz.com/edit/react-docx-images (adding images to Word Document)

Here is an example of `docx` working in `Vue.js`:

-   https://stackblitz.com/edit/vuejs-docx

## Node

Press `endpoint` on the `RunKit` website:

![RunKit Instructions](https://user-images.githubusercontent.com/2917613/38582539-f84311b6-3d07-11e8-90db-5885ae02c3c4.png)

-   https://runkit.com/dolanmiu/docx-demo1 - Simple paragraph and text
-   https://runkit.com/dolanmiu/docx-demo2 - Advanced Paragraphs and text
-   https://runkit.com/dolanmiu/docx-demo3 - Bullet points
-   https://runkit.com/dolanmiu/docx-demo4 - Simple table
-   https://runkit.com/dolanmiu/docx-demo5 - Images
-   https://runkit.com/dolanmiu/docx-demo6 - Margins
-   https://runkit.com/dolanmiu/docx-demo7 - Landscape
-   https://runkit.com/dolanmiu/docx-demo8 - Header and Footer
-   https://runkit.com/dolanmiu/docx-demo10 - **My CV generated with docx**

More [here](https://github.com/dolanmiu/docx/tree/master/demo)

# How to use & Documentation

Please refer to the [documentation at https://docx.js.org/](https://docx.js.org/) for details on how to use this library, examples and much more!

# Examples

Check the [demo folder](https://github.com/dolanmiu/docx/tree/master/demo) for examples.

# Contributing

Read the contribution guidelines [here](https://docx.js.org/#/contribution-guidelines).

# Used by

[<img src="https://i.imgur.com/zy5qWmI.png" alt="drawing" height="50"/>](https://hfour.com/)
[<img src="https://i.imgur.com/OYP5tgS.png" alt="drawing" height="50"/>](https://fuzzproductions.com/)
[<img src="https://i.imgur.com/zUDMfZ3.png" alt="drawing" height="50"/>](https://www.mettzer.com/)
[<img src="https://i.imgur.com/wtNB1uq.png" alt="drawing" height="50"/>](https://www.wisedoc.net/)
[<img src="https://i.imgur.com/suiH2zc.png" alt="drawing" height="50"/>](https://www.dabblewriter.com/)
[<img src="https://i.imgur.com/1LjuK2M.png" alt="drawing" height="50"/>](https://turbopatent.com/)
[<img src="https://i.imgur.com/dHMg0wF.gif" alt="drawing" height="50"/>](http://www.madisoncres.com/)
[<img src="https://i.imgur.com/QEZXU5b.png" alt="drawing" height="50"/>](https://www.beekast.com/)
[<img src="https://i.imgur.com/XVU6aoi.png" alt="drawing" height="50"/>](https://herraizsoto.com/)
[<img src="https://i.imgur.com/fn1xccG.png" alt="drawing" height="50"/>](http://www.ativer.com.br/)
[<img src="https://i.imgur.com/cmykN7c.png" alt="drawing"/>](https://www.arity.co/)
[<img src="https://i.imgur.com/PXo25um.png" alt="drawing" height="50"/>](https://www.circadianrisk.com/)
[<img src="https://i.imgur.com/AKGhtlh.png" alt="drawing"/>](https://lexense.com/)
[<img src="https://i.imgur.com/9tqJaHw.png" alt="drawing" height="50"/>](https://novelpad.co/)

...and many more!

---

[![patreon][patreon-image]][patreon-url]
[![browserstack][browserstack-image]][browserstack-url]

Made with 💖

[npm-image]: https://badge.fury.io/js/docx.svg
[npm-url]: https://npmjs.org/package/docx
[downloads-image]: https://img.shields.io/npm/dm/docx.svg
[downloads-url]: https://npmjs.org/package/docx
[github-actions-workflow-image]: https://github.com/dolanmiu/docx/workflows/Default/badge.svg
[github-actions-workflow-url]: https://github.com/dolanmiu/docx/actions
[snky-image]: https://snyk.io/test/github/dolanmiu/docx/badge.svg
[snky-url]: https://snyk.io/test/github/dolanmiu/docx
[pr-image]: https://img.shields.io/badge/PRs-welcome-brightgreen.svg
[pr-url]: http://makeapullrequest.com
[codecov-image]: https://codecov.io/gh/dolanmiu/docx/branch/master/graph/badge.svg
[codecov-url]: https://codecov.io/gh/dolanmiu/docx
[patreon-image]: https://user-images.githubusercontent.com/2917613/51251459-4e880480-1991-11e9-92bf-38b96675a9e2.png
[patreon-url]: https://www.patreon.com/dolanmiu
[browserstack-image]: https://user-images.githubusercontent.com/2917613/54233552-128e9d00-4505-11e9-88fb-025a4e04007c.png
[browserstack-url]: https://www.browserstack.com
