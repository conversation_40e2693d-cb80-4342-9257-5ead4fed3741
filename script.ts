#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';
import mammoth from 'mammoth';
import xlsx from 'xlsx';
// import pdf from 'pdf-parse'; // Temporarily disabled due to import issues

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Command line argument parsing
const args = process.argv.slice(2);
let inputFolder = null;
let outputFile = 'investment-memo.docx';

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--input' || args[i] === '-i') {
    inputFolder = args[i + 1];
    i++;
  } else if (args[i] === '--output' || args[i] === '-o') {
    outputFile = args[i + 1];
    i++;
  } else if (args[i] === '--help' || args[i] === '-h') {
    console.log(`
Investment Memo Generator

Usage: node investment-memo-generator.js --input <folder> [--output <filename>]

Options:
  -i, --input <folder>    Input folder containing documents to analyze
  -o, --output <filename> Output Word document filename (default: investment-memo.docx)
  -h, --help             Show this help message

Example:
  node investment-memo-generator.js --input ./documents --output my-memo.docx
    `);
    process.exit(0);
  }
}

if (!inputFolder) {
  console.error('Error: Please provide an input folder using --input flag');
  console.log('Use --help for usage information');
  process.exit(1);
}

// File type handlers
const fileHandlers = {
  // '.pdf': extractPDF, // Temporarily disabled
  '.docx': extractDOCX,
  '.doc': extractDOCX,
  '.xlsx': extractXLSX,
  '.xls': extractXLSX,
  '.txt': extractTXT,
  '.md': extractTXT,
  '.csv': extractCSV
};

async function main() {
  try {
    console.log('🏠 Investment Memo Generator Starting...\n');

    // Check if input folder exists
    try {
      await fs.access(inputFolder);
    } catch (error) {
      console.error(`❌ Error: Input folder "${inputFolder}" does not exist`);
      process.exit(1);
    }

    // Check if OpenRouter API key is set
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    if (!openRouterApiKey) {
      console.error('❌ Error: OPENROUTER_API_KEY environment variable is not set');
      console.log('Please set your OpenRouter API key: export OPENROUTER_API_KEY=your_key_here');
      process.exit(1);
    }

    console.log(`📁 Processing files from: ${inputFolder}`);
    
    // Read and process all files in the input folder
    const files = await getAllFiles(inputFolder);
    console.log(`📄 Found ${files.length} files to process\n`);

    if (files.length === 0) {
      console.error('❌ No supported files found in the input folder');
      console.log('Supported file types: .pdf, .docx, .doc, .xlsx, .xls, .txt, .md, .csv');
      process.exit(1);
    }

    // Extract content from all files
    const extractedContents = {};
    const filesByCategory = categorizeFiles(files);

    for (const file of files) {
      try {
        console.log(`📖 Processing: ${path.basename(file)}`);
        const content = await extractFileContent(file);
        extractedContents[file] = content;
        console.log(`✅ Extracted ${content.length} characters`);
      } catch (error) {
        console.error(`❌ Failed to process ${path.basename(file)}: ${error.message}`);
        extractedContents[file] = `[Content extraction failed for ${path.basename(file)}: ${error.message}]`;
      }
    }

    console.log('\n🤖 Generating investment memo with Gemini 2.5 Pro...');

    // Create analysis prompt
    const analysisPrompt = createAnalysisPrompt(filesByCategory, extractedContents);

    // Generate memo using OpenRouter
    const memoContent = await generateMemoWithOpenRouter(analysisPrompt, openRouterApiKey);

    console.log('📝 Creating Word document...');

    // Generate Word document
    const wordDoc = await generateWordDocument(memoContent);

    // Save the document
    await fs.writeFile(outputFile, wordDoc);

    console.log(`\n🎉 Investment memo generated successfully!`);
    console.log(`📄 Output saved to: ${outputFile}`);
    console.log(`📊 Processed ${files.length} files`);
    console.log(`📏 Generated memo: ${memoContent.length} characters`);

  } catch (error) {
    console.error('❌ Error generating investment memo:', error.message);
    process.exit(1);
  }
}

async function getAllFiles(folderPath) {
  const allFiles = [];
  
  async function scanDirectory(dirPath) {
    const items = await fs.readdir(dirPath, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name);
      
      if (item.isDirectory()) {
        await scanDirectory(fullPath);
      } else if (item.isFile()) {
        const ext = path.extname(item.name).toLowerCase();
        if (fileHandlers[ext]) {
          allFiles.push(fullPath);
        }
      }
    }
  }
  
  await scanDirectory(folderPath);
  return allFiles;
}

function categorizeFiles(files) {
  const categories = {
    financial: [],
    property: [],
    market: [],
    legal: [],
    other: []
  };

  for (const file of files) {
    const fileName = path.basename(file).toLowerCase();
    const ext = path.extname(file).toLowerCase();

    if (fileName.includes('financial') || fileName.includes('budget') || fileName.includes('cash') || ext === '.xlsx' || ext === '.xls' || ext === '.csv') {
      categories.financial.push(file);
    } else if (fileName.includes('property') || fileName.includes('building') || fileName.includes('inspection') || fileName.includes('appraisal')) {
      categories.property.push(file);
    } else if (fileName.includes('market') || fileName.includes('comp') || fileName.includes('analysis') || fileName.includes('research')) {
      categories.market.push(file);
    } else if (fileName.includes('legal') || fileName.includes('contract') || fileName.includes('lease') || fileName.includes('title')) {
      categories.legal.push(file);
    } else {
      categories.other.push(file);
    }
  }

  return categories;
}

async function extractFileContent(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const handler = fileHandlers[ext];
  
  if (!handler) {
    throw new Error(`Unsupported file type: ${ext}`);
  }
  
  return await handler(filePath);
}

// File extraction functions
// async function extractPDF(filePath) {
//   const dataBuffer = await fs.readFile(filePath);
//   const data = await pdf(dataBuffer);
//   return data.text;
// }

async function extractDOCX(filePath) {
  const dataBuffer = await fs.readFile(filePath);
  const result = await mammoth.extractRawText({ buffer: dataBuffer });
  return result.value;
}

async function extractXLSX(filePath) {
  const workbook = xlsx.readFile(filePath);
  let content = '';
  
  workbook.SheetNames.forEach(sheetName => {
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
    
    content += `\n--- Sheet: ${sheetName} ---\n`;
    jsonData.forEach(row => {
      if (row.length > 0) {
        content += row.join('\t') + '\n';
      }
    });
  });
  
  return content;
}

async function extractTXT(filePath) {
  return await fs.readFile(filePath, 'utf-8');
}

async function extractCSV(filePath) {
  const content = await fs.readFile(filePath, 'utf-8');
  return content;
}

function createAnalysisPrompt(filesByCategory, extractedContents) {
  let prompt = `Please analyze the following real estate investment opportunity based on the uploaded documents and generate a comprehensive Investment Memorandum.

DOCUMENT INVENTORY AND CONTENT:
`;

  // Add file inventory by category with extracted content
  Object.entries(filesByCategory).forEach(([category, files]) => {
    if (files.length === 0) return;
    
    const categoryName = getCategoryName(category);
    prompt += `\n${categoryName.toUpperCase()} (${files.length} files):
`;
    
    files.forEach((file) => {
      const fileName = path.basename(file);
      prompt += `\n--- ${fileName} ---\n`;
      const content = extractedContents[file];
      if (content && !content.startsWith('[Content extraction failed')) {
        prompt += content + '\n';
      } else {
        prompt += `[Content extraction failed or unavailable for ${fileName}]\n`;
      }
      prompt += `--- End of ${fileName} ---\n`;
    });
  });

  prompt += `

INVESTMENT MEMORANDUM REQUIREMENTS:

Please generate a comprehensive investment memorandum with the following structure using proper Markdown formatting:

## Executive Summary
- Investment opportunity overview
- Key investment highlights
- Financial summary
- Risk assessment summary
- Investment recommendation

## Property Overview
- Property description and location
- Physical characteristics
- Current condition and improvements needed
- Zoning and land use

## Market Analysis
- Local market conditions
- Comparable properties analysis
- Market trends and projections
- Competitive landscape

## Financial Analysis
- Purchase price and acquisition costs
- Operating income and expenses
- Cash flow projections
- Return metrics (IRR, NPV, Cap Rate, etc.)
- Sensitivity analysis

## Investment Strategy
- Hold period and exit strategy
- Value creation opportunities
- Capital improvements plan
- Financing structure

## Risk Analysis
- Market risks
- Property-specific risks
- Financial risks
- Mitigation strategies

## Conclusion and Recommendation
- Investment thesis
- Expected returns
- Risk-adjusted recommendation

Please provide detailed analysis based on the document types available. If certain information is not available from the uploaded documents, please note what additional information would be needed for a complete analysis.

Use proper Markdown formatting with headers (##, ###), bullet points (-), **bold text**, *italic text*, and organize information clearly. Make the memo professional, comprehensive, and suitable for presentation to institutional investors or investment committees.
`;

  return prompt;
}

function getCategoryName(category) {
  const categoryNames = {
    financial: 'Financial Documents',
    property: 'Property Analysis',
    market: 'Market Research',
    legal: 'Legal Documents',
    other: 'Other Documents'
  };
  return categoryNames[category] || category;
}

async function generateMemoWithOpenRouter(prompt, apiKey) {
  const maxRetries = 3;
  let lastError = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 API call attempt ${attempt}/${maxRetries}`);
      
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://local-investment-memo-generator',
          'X-Title': 'Investment Memo Generator'
        },
        body: JSON.stringify({
          model: 'anthropic/claude-3.5-sonnet',
          messages: [
            {
              role: 'system',
              content: 'You are an expert real estate investment analyst. Generate a comprehensive, professional investment memorandum based on the uploaded documents. The memo should be detailed, well-structured, and suitable for institutional investors. Format your response using proper Markdown syntax with headers (##, ###), bullet points, bold text (**text**), italic text (*text*), and tables where appropriate.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 4000,
          temperature: 0.1,
          stream: false
        })
      });

      if (response.ok) {
        const aiResponse = await response.json();
        return aiResponse.choices[0].message.content;
      } else {
        const errorText = await response.text();
        console.error(`❌ API error (attempt ${attempt}):`, response.status, errorText);
        
        if (response.status === 429 || response.status === 502) {
          lastError = new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
          if (attempt < maxRetries) {
            const waitTime = Math.pow(2, attempt) * 1000;
            console.log(`⏳ Waiting ${waitTime}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            continue;
          }
        } else {
          throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
        }
      }
    } catch (error) {
      console.error(`❌ Error on attempt ${attempt}:`, error.message);
      lastError = error;
      if (attempt < maxRetries) {
        const waitTime = Math.pow(2, attempt) * 1000;
        console.log(`⏳ Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  throw lastError || new Error('All retry attempts failed');
}

async function generateWordDocument(content) {
  // Parse markdown content and convert to Word document elements
  const elements = parseMarkdownToWordElements(content);
  
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        // Title
        new Paragraph({
          children: [
            new TextRun({
              text: "INVESTMENT MEMORANDUM",
              bold: true,
              size: 32
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 400
          }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: `Generated on ${new Date().toLocaleDateString()}`,
              italics: true,
              size: 20
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 800
          }
        }),
        
        // Rendered markdown elements
        ...elements
      ]
    }],
    numbering: {
      config: [{
        reference: "default-numbering",
        levels: [
          {
            level: 0,
            format: "decimal",
            text: "%1.",
            alignment: "start"
          },
          {
            level: 1,
            format: "lowerLetter", 
            text: "%2.",
            alignment: "start"
          },
          {
            level: 2,
            format: "lowerRoman",
            text: "%3.",
            alignment: "start"
          }
        ]
      }]
    }
  });

  return await Packer.toBuffer(doc);
}

function parseMarkdownToWordElements(content) {
  const elements = [];
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (!line.trim()) {
      // Empty line - add small spacing
      elements.push(new Paragraph({
        children: [new TextRun({ text: "" })],
        spacing: { after: 100 }
      }));
      continue;
    }
    
    // Headers
    if (line.startsWith('# ')) {
      elements.push(new Paragraph({
        children: [
          new TextRun({
            text: line.substring(2),
            bold: true,
            size: 32
          })
        ],
        heading: HeadingLevel.HEADING_1,
        spacing: {
          before: 600,
          after: 300
        }
      }));
    } else if (line.startsWith('## ')) {
      elements.push(new Paragraph({
        children: [
          new TextRun({
            text: line.substring(3),
            bold: true,
            size: 28
          })
        ],
        heading: HeadingLevel.HEADING_2,
        spacing: {
          before: 400,
          after: 200
        }
      }));
    } else if (line.startsWith('### ')) {
      elements.push(new Paragraph({
        children: [
          new TextRun({
            text: line.substring(4),
            bold: true,
            size: 24
          })
        ],
        heading: HeadingLevel.HEADING_3,
        spacing: {
          before: 300,
          after: 150
        }
      }));
    } else if (line.startsWith('#### ')) {
      elements.push(new Paragraph({
        children: [
          new TextRun({
            text: line.substring(5),
            bold: true,
            size: 22
          })
        ],
        heading: HeadingLevel.HEADING_4,
        spacing: {
          before: 200,
          after: 100
        }
      }));
    }
    // Bullet points
    else if (line.match(/^[\s]*[-*+]\s/)) {
      const indent = (line.match(/^(\s*)/)[1].length / 2) * 360; // Convert spaces to twips
      const text = line.replace(/^[\s]*[-*+]\s/, '');
      
      elements.push(new Paragraph({
        children: parseInlineMarkdown(text),
        bullet: {
          level: Math.floor(indent / 360)
        },
        spacing: {
          after: 100
        }
      }));
    }
    // Numbered lists
    else if (line.match(/^[\s]*\d+\.\s/)) {
      const indent = (line.match(/^(\s*)/)[1].length / 2) * 360;
      const text = line.replace(/^[\s]*\d+\.\s/, '');
      
      elements.push(new Paragraph({
        children: parseInlineMarkdown(text),
        numbering: {
          reference: "default-numbering",
          level: Math.floor(indent / 360)
        },
        spacing: {
          after: 100
        }
      }));
    }
    // Regular paragraphs
    else {
      elements.push(new Paragraph({
        children: parseInlineMarkdown(line),
        spacing: {
          after: 150
        }
      }));
    }
  }
  
  return elements;
}

function parseInlineMarkdown(text) {
  const runs = [];
  let currentPos = 0;
  
  // Pattern to match **bold**, *italic*, and `code`
  const pattern = /(\*\*.*?\*\*|\*.*?\*|`.*?`)/g;
  let match;
  
  while ((match = pattern.exec(text)) !== null) {
    // Add text before the match
    if (match.index > currentPos) {
      runs.push(new TextRun({
        text: text.substring(currentPos, match.index),
        size: 22
      }));
    }
    
    const matchedText = match[0];
    
    if (matchedText.startsWith('**') && matchedText.endsWith('**')) {
      // Bold text
      runs.push(new TextRun({
        text: matchedText.slice(2, -2),
        bold: true,
        size: 22
      }));
    } else if (matchedText.startsWith('*') && matchedText.endsWith('*')) {
      // Italic text
      runs.push(new TextRun({
        text: matchedText.slice(1, -1),
        italics: true,
        size: 22
      }));
    } else if (matchedText.startsWith('`') && matchedText.endsWith('`')) {
      // Code text
      runs.push(new TextRun({
        text: matchedText.slice(1, -1),
        font: "Courier New",
        size: 20
      }));
    }
    
    currentPos = match.index + matchedText.length;
  }
  
  // Add remaining text
  if (currentPos < text.length) {
    runs.push(new TextRun({
      text: text.substring(currentPos),
      size: 22
    }));
  }
  
  return runs.length > 0 ? runs : [new TextRun({ text: text, size: 22 })];
}

// Run the script
main().catch(console.error);